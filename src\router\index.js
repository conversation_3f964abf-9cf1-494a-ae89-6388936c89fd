import { createRouter } from 'vue-router'
import { createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import DashboardView from '../views/DashboardView.vue'
import ForcePasswordChangeView from '../views/ForcePasswordChangeView.vue'
import { useAuthStore } from '@/stores/auth'

// 문의하기 기능 라우트 목록 가져오기
import inquiryRoutes from './inquiry-routes'
// QnA 기능 라우트 목록 가져오기
import qnaRoutes from './qna-routes'

const routes = [
  {
    path: '/',
    name: 'root',
    redirect: () => {
      const authStore = useAuthStore();
      return authStore.isAuthenticated ? { name: 'dashboard' } : { name: 'login' };
    }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    meta: { requiresGuest: true }
  },
  {
    path: '/force-password-change',
    name: 'force-password-change',
    component: ForcePasswordChangeView,
    meta: { requiresPasswordChange: true }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: DashboardView,
    meta: {
      requiresAuth: true,
      title: '대시보드'
      // 모든 인증된 사용자 접근 가능
     }
  },
  {
    path: '/project/list', // 프로젝트 관리 라우트 추가
    name: 'project-management',
    component: () => import('@/views/ProjectManagementView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN'], // SUPER_ADMIN만 접근 가능
      title: '프로젝트 관리'
    }
  },
  {
    path: '/projects/edit/:projectId?', // 프로젝트 생성/수정 라우트 추가
    name: 'project-form',
    component: () => import('@/views/project/CreateProject.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN'],
    }
  },
  {
    path: '/admin/users', // 사용자 관리 라우트 경로 유지
    name: 'admin-user-management', // 이름 변경 (admin 접두사 추가)
    // 생성한 컴포넌트 경로로 수정
    component: () => import('@/views/admin/usermanagement/UserManagement.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN'], // SUPER_ADMIN만 접근 가능
      title: '관리자 관리'
    }
  },
  {
    path: '/admin/users/edit/:userEmail?', // 파라미터를 userEmail로 변경
    name: 'admin-user-form', // 라우트 이름 변경 (생성/수정 폼)
    component: () => import('@/views/admin/usermanagement/CreateUser.vue'), // 동일 컴포넌트 사용
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN'], // SUPER_ADMIN만 접근 가능
      // title 속성은 제거된 상태 유지 (사이드바에 노출되지 않음)
    }
  },
  {
    path: '/landing-management', // 랜딩 페이지 관리 라우트 추가
    name: 'landing-management',
    component: () => import('@/views/landing/LandingManagementView.vue'), // Lazy load
    meta: {
      requiresAuth: true,
      // SUPER_ADMIN, PROJECT_ADMIN, SUB_ADMIN 접근 가능
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '랜딩 페이지 관리'
    }
  },
  {
    path: '/qr-management', // QR 관리 상위 메뉴
    name: 'qr-management',
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: 'QR 관리'
    },
    children: [
      {
        path: '', // /qr-management (기본 경로)
        redirect: 'list'
      },
      {
        path: 'list', // /qr-management/list
        name: 'qr-list',
        component: () => import('@/views/qr/QrManagementView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: 'QR코드 목록'
        }
      },
      {
        path: 'scan', // /qr-management/scan
        name: 'qr-scan',
        component: () => import('@/views/admin/qrscan/QrScanView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: 'QR코드 스캔 바로가기',
          openInNewTab: true,
          hideMenu: true // 사이드바 숨김 처리
        }
      }
    ]
  },
  {
    path: '/qr/view/:qrCodeId', // QR 코드 상세 보기 라우트
    name: 'qr-view',
    component: () => import('@/views/qr/QrCodeView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN']
    }
  },
  {
    path: '/qr/edit/:qrCodeId?', // QR 코드 생성/수정 라우트
    name: 'qr-form',
    component: () => import('@/views/qr/QrCodeForm.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN']
    }
  },
  {
    path: '/landing/view/:landingPageId', // 랜딩 페이지 상세 보기 라우트
    name: 'landing-view',
    component: () => import('@/views/landing/LandingView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN']
    }
  },
  {
    path: '/landing/edit/:landingPageId?', // 랜딩 페이지 생성/수정 라우트
    name: 'landing-form',
    component: () => import('@/views/landing/LandingForm.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN']
    }
  },
  {
    path: '/landing/:id', // 공개 랜딩 페이지 라우트 (인증 불필요)
    name: 'public-landing-page',
    component: () => import('@/views/landing/PublicLandingPage.vue'),
    meta: {
      requiresAuth: false, // 인증이 필요하지 않음 (공개 페이지)
      public: true // 공개 페이지임을 명시적으로 표시
    }
  },
  {
    path: '/events-management',
    name: 'events-management',
    component: () => import('@/views/events/EventLayout.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '이벤트 관리'
    },
    children: [
      {
        path: '/events',
        name: 'events-management-list',
        component: () => import('@/views/events/EventsManagementView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: '이벤트 목록'
        }
      },
      {
        path: '/pre-registration',
        name: 'pre-registration-management',
        component: () => import('@/views/admin/preRegistrations/PreRegistrationManagementView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: '이벤트 사전 신청서 관리'
        }
      },
      {
        path: '/attendees',
        name: 'attendees-management',
        component: () => import('@/views/admin/attendees/AttendeesManagementView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN'],
          title: '이벤트 참가자 관리'
        }
      },
      {
        path: '/attendees/register',
        name: 'attendee-registration',
        component: () => import('@/views/admin/attendees/AttendeeRegistrationView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN']
          // title을 제거하여 사이드네비에 표시되지 않도록 함
        }
      },
      {
        path: '/events/teams', // 이벤트 참가 팀 관리 경로
        name: 'event-team-management',
        component: () => import('@/views/admin/team/EventTeamManagementView.vue'), // 새로운 뷰 컴포넌트 경로
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: '이벤트 참가 팀 관리'
        }
      },
      {
        path: '/events/teams/create',
        name: 'event-team-create',
        component: () => import('@/views/admin/team/EventTeamCreateView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: undefined
        }
      },
      {
        path: '/events/teams/:id',
        name: 'event-team-detail',
        component: () => import('@/views/admin/team/EventTeamDetailView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: undefined
        }
      }
    ]
  },
  {
    path: '/events/create',
    name: 'event-create',
    component: () => import('@/views/events/EventFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: undefined // 사이드바 노출 제외
    }
  },
  {
    path: '/events/:eventId',
    name: 'event-view',
    component: () => import('@/views/events/EventDetailView.vue'),
    meta: { requiresAuth: true, permissions: ['SUPER_ADMIN','PROJECT_ADMIN','SUB_ADMIN'], title: undefined }
  },
  {
    path: '/events/edit/:eventId',
    name: 'event-edit',
    component: () => import('@/views/events/EventFormView.vue'),
    meta: { requiresAuth: true, permissions: ['SUPER_ADMIN','PROJECT_ADMIN','SUB_ADMIN'], title: undefined }
  },
  {
    path: '/event/:id', // 공개 이벤트 라우트 (인증 불필요)
    name: 'public-event-page',
    component: () => import('@/views/events/PublicEventPage.vue'),
    meta: {
      requiresAuth: false, // 인증이 필요하지 않음 (공개 페이지)
      public: true // 공개 페이지임을 명시적으로 표시
    }
  },
  {
    path: '/pre-registration/create',
    name: 'pre-registration-create',
    component: () => import('@/views/admin/preRegistrations/PreRegistrationFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: undefined
    }
  },
  {
    path: '/pre-registration/:formId',
    name: 'pre-registration-view',
    component: () => import('@/views/admin/preRegistrations/PreRegistrationDetailView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: undefined
    }
  },
  {
    path: '/pre-registration/edit/:formId',
    name: 'pre-registration-edit',
    component: () => import('@/views/admin/preRegistrations/PreRegistrationFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: undefined
    }
  },
  // 통계 관련 라우트
  {
    path: '/statistics',
    name: 'statistics',
    component: () => import('@/views/statistics/StatisticsLayout.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '통계'
    },
    children: [
      {
        path: 'qr',
        name: 'statistics-qr',
        component: () => import('@/views/statistics/QrStatisticsView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: 'QR코드 통계'
        }
      },
      {
        path: 'admin',
        name: 'statistics-admin',
        component: () => import('@/views/statistics/AdminStatisticsView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN'],
          title: '관리자 통계'
        }
      },
      {
        path: 'usage',
        name: 'statistics-usage',
        component: () => import('@/views/statistics/UsageStatisticsView.vue'),
        meta: {
          requiresAuth: true,
          permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
          title: '사용 통계'
        }
      }
    ]
  },
  // 문의 목록 (SUPER_ADMIN: 모든 문의, PROJECT_ADMIN/SUB_ADMIN: 자신의 프로젝트 문의)
  {
    path: '/inquiries',
    name: 'inquiry-list',
    component: () => import('@/views/inquiry/InquiryListView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: '문의하기'
    }
  },
  // 새 문의 작성 (PROJECT_ADMIN, SUB_ADMIN만 작성 가능)
  {
    path: '/inquiries/new',
    name: 'inquiry-new',
    component: () => import('@/views/inquiry/InquiryFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['PROJECT_ADMIN', 'SUB_ADMIN'],
      title: undefined // 사이드바에 표시하지 않음
    }
  },
  // 문의 상세 (목록 조회 권한자 + 해당 문의 작성자)
  {
    path: '/inquiries/:id',
    name: 'inquiry-detail',
    component: () => import('@/views/inquiry/InquiryDetailView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: undefined // 사이드바에 표시하지 않음
    }
  },
  // 문의 수정 (문의 작성자만 가능)
  {
    path: '/inquiries/:id/edit',
    name: 'inquiry-edit',
    component: () => import('@/views/inquiry/InquiryFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['PROJECT_ADMIN', 'SUB_ADMIN'],
      title: undefined // 사이드바에 표시하지 않음
    }
  },
  // QnA 관리 (단일 메뉴)
  {
    path: '/qna',
    name: 'qna-management',
    component: () => import('@/views/qna/QnaListView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN'],
      title: 'QnA'
    }
  },
  // QnA 상세
  {
    path: '/qna/:questionId',
    name: 'qna-detail',
    component: () => import('@/views/qna/QnaDetailView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN']
    }
  },
  // QnA 생성
  {
    path: '/qna/create',
    name: 'qna-create',
    component: () => import('@/views/qna/QnaFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN']
    }
  },
  // QnA 수정
  {
    path: '/qna/edit/:questionId',
    name: 'qna-edit',
    component: () => import('@/views/qna/QnaFormView.vue'),
    meta: {
      requiresAuth: true,
      permissions: ['SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN']
    }
  },

];

const router = createRouter({
  // 히스토리 모드 사용 (새로고침 시 404 문제는 서버 설정으로 해결)
  history: createWebHistory(),
  routes,
})

// 히스토리 모드로 돌아가려면 아래 코드를 사용하세요 (서버 설정 필요):
// import { createWebHistory } from 'vue-router'
// const router = createRouter({
//   history: createWebHistory(import.meta.env.BASE_URL),
//   routes,
// })

// router.beforeEach 네비게이션 가드는 변경 없이 그대로 사용합니다.
// 권한 체크 로직은 이미 meta.permissions를 기반으로 동작합니다.
router.beforeEach((to, _from, next) => {
  const authStore = useAuthStore();
  const isAuthenticated = authStore.isAuthenticated;
  
  // 사용자 역할 가져오는 방식 수정 - roleId가 실제 역할 코드인지 확인하고, 아니면 roleName 사용
  const userRole = authStore.user?.roleName || authStore.user?.roleId;
  
  const isForcePasswordChange = authStore.isForcePasswordChange;
  const isLoading = authStore.isLoading;

  // 로그인 페이지, 비밀번호 변경 페이지, 공개 랜딩 페이지, 공개 이벤트 페이지로 이동하는 경우 항상 허용
  if (to.name === 'login' || to.name === 'force-password-change' || to.name === 'public-landing-page' || to.name === 'public-event-page') {
    return next();
  }

  // 공개 페이지로 이동하는 경우 인증 검사 우회
  if (to.meta && to.meta.public === true) {
    return next();
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest);
  const requiresPasswordChange = to.matched.some(record => record.meta.requiresPasswordChange);
  const requiredPermissions = to.meta.permissions;

  // 비밀번호 강제 변경 상태 처리
  if (isForcePasswordChange) {
    // 다른 모든 페이지는 비밀번호 변경 페이지로 리다이렉트
    return next({ name: 'force-password-change' });
  }

  // 비밀번호 변경 페이지 접근 제한 (강제 변경 상태가 아닐 때)
  if (requiresPasswordChange && !isForcePasswordChange) {
    return next({ name: 'login' });
  }

  // 루트 경로 리다이렉션 (기존과 동일)
  if (to.path === '/' && !isAuthenticated) {
    return next({ name: 'login' });
  } else if (to.path === '/' && isAuthenticated) {
    return next({ name: 'dashboard' });
  }

  // 인증 및 권한 체크
  if (requiresAuth && !isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } });
  } else if (requiresGuest && isAuthenticated) {
    next({ name: 'dashboard' });
  } else if (requiresAuth && requiredPermissions && requiredPermissions.length > 0) {
    // 필요한 권한이 정의된 경우
    // 권한 확인 부분 개선
    const hasPermission = () => {
      if (!userRole) return false;
      
      // SUPER_ADMIN 특별 처리 - 유형에 관계없이 SUPER_ADMIN이면 문의 생성 권한 부여
      if (userRole === 'SUPER_ADMIN' && to.name === 'inquiry-new') {
        return true;
      }
      
      // 일반적인 권한 검사
      return requiredPermissions.includes(userRole);
    };
    
    if (!hasPermission()) {
      // 사용자 역할이 없거나 필요한 권한이 없는 경우
      console.warn(`Navigation blocked: User role (${userRole}) does not have permission for ${to.fullPath}. Required: ${requiredPermissions.join(', ')}`);
      // 접근 불가 시 대시보드로 리다이렉트 (또는 403 페이지)
      next({ name: 'dashboard' });
    } else {
      // 권한이 있는 경우
      next();
    }
  } else {
    // 인증만 필요하거나 인증/권한이 모두 필요 없는 경우
    next();
  }
});

export default router
