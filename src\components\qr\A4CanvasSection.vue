<template>
  <div class="a4-canvas-section" v-if="!isEditMode">
    <!-- 캔버스 박스 사용 토글 -->
    <div class="form-group design-group">
      <div class="toggle-container">
        <label for="useA4Canvas">캔버스 박스 사용</label>
        <div class="toggle-switch">
          <input type="checkbox" id="useA4Canvas" v-model="useA4Canvas" :disabled="isEditMode" />
          <label for="useA4Canvas"></label>
        </div>
      </div>
      <div class="field-note">
        자유롭게 크기를 조절할 수 있는 캔버스 박스를 사용하여 QR코드와 배경 이미지 위치를 정확히 설정할 수 있습니다.
      </div>
    </div>

    <!-- 배경 이미지 추가 토글 (A4 박스 사용 시에만 표시) -->
    <div class="form-group design-group" v-if="useA4Canvas">
      <div class="toggle-container">
        <label for="useBackgroundImage">배경 이미지 추가</label>
        <div class="toggle-switch">
          <input type="checkbox" id="useBackgroundImage" v-model="useBackgroundImage" :disabled="isEditMode" />
          <label for="useBackgroundImage"></label>
        </div>
      </div>
      <div v-if="useBackgroundImage" class="background-image-container">
        <div class="background-image-controls">
          <div v-if="backgroundImagePreview" class="bg-image-info">
            <span class="bg-image-name">이미지: {{ backgroundImageFile ? backgroundImageFile.name : '배경 이미지' }}</span>
            <button type="button" class="secondary-btn remove-bg-btn-outside" @click="removeBackgroundImage" :disabled="isEditMode">배경 제거</button>
          </div>
          <div v-else class="bg-upload-outside">
            <label for="backgroundFile" class="background-upload-btn">
              <span class="upload-icon">+</span>
              <span>배경 이미지 선택</span>
            </label>
            <input
              type="file"
              id="backgroundFile"
              ref="backgroundInput"
              @change="handleBackgroundUpload"
              accept="image/png,image/jpeg,image/svg+xml"
              class="background-file-input"
              :disabled="isEditMode"
            />
          </div>

          <div v-if="backgroundImagePreview" class="canvas-toolbar">
            <span>이미지 조절:</span>
            <div class="image-controls">
              <button
                type="button"
                class="control-btn"
                :class="{active: backgroundFitMode === 'fill'}"
                @click="setBackgroundFitMode('fill')"
              >
                채우기
              </button>
              <button
                type="button"
                class="control-btn"
                :class="{active: backgroundFitMode === 'fit'}"
                @click="setBackgroundFitMode('fit')"
              >
                맞추기
              </button>
              <button
                type="button"
                class="control-btn"
                :class="{active: backgroundFitMode === 'original'}"
                @click="setBackgroundFitMode('original')"
              >
                원본
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 캔버스 박스 (A4 박스 사용 시에만 표시) -->
    <div v-if="useA4Canvas" class="form-group a4-canvas-section">
      <h4>캔버스 박스</h4>
      <div class="canvas-size-display">
        크기: {{ Math.round(a4CanvasWidth) }} x {{ Math.round(a4CanvasHeight) }} px 
        ({{ cmA4CanvasWidth }} x {{ cmA4CanvasHeight }} cm)
      </div>
      <div class="a4-canvas">
        <!-- A4 캔버스 부모 컨테이너 -->
        <div class="a4-canvas-parent-container" data-a4-canvas-parent-container>
          <!-- A4 캔버스 (크기 조절만 가능) -->
          <DraggableResizableVue3
            :parent="true"
            v-model:x="a4CanvasX"
            v-model:y="a4CanvasY"
            v-model:w="a4CanvasWidth"
            v-model:h="a4CanvasHeight"
            :lockAspectRatio="false"
            :draggable="false"
            :resizable="true"
            class="a4-canvas-draggable-container"
          >
          <div class="canvas-container" ref="a4CanvasContainer">
            <!-- 배경 이미지 (크기 조절 가능) -->
            <div v-if="useBackgroundImage && backgroundImagePreview" class="background-container-wrapper">
              <DraggableResizableVue3
                v-model:x="backgroundPositionX"
                v-model:y="backgroundPositionY"
                v-model:w="backgroundWidth"
                v-model:h="backgroundHeight"
                :minW="50"
                :minH="50"
                :lockAspectRatio="false"
                :class="{ 'inactive-draggable': !isBackgroundImageActive }"
                @activated="isBackgroundImageActive = true; isQrCodeActive = false"
                @deactivated="isBackgroundImageActive = false"
              >
                <div class="background-on-canvas">
                  <img
                    :src="backgroundImagePreview"
                    alt="Background Image on Canvas"
                    class="background-image-on-canvas"
                    :style="backgroundImageStyle"
                    @error="handleImageError"
                  />
                </div>
              </DraggableResizableVue3>
            </div>

            <!-- QR 코드 (크기 조절 가능) -->
            <div class="qr-container-wrapper" v-if="qrCodeData && !imageLoadError">
              <DraggableResizableVue3
                v-model:x="qrPositionX"
                v-model:y="qrPositionY"
                v-model:w="qrWidth"
                v-model:h="qrHeight"
                :minW="50"
                :minH="50"
                :maxW="200"
                :maxH="200"
                :lockAspectRatio="true"
                :class="{ 'inactive-draggable': !isQrCodeActive }"
                @activated="isQrCodeActive = true; isBackgroundImageActive = false"
                @deactivated="isQrCodeActive = false"
              >
                <div class="qr-on-canvas">
                  <img
                    v-if="qrCodeData"
                    :src="qrCodeData"
                    alt="QR Code on Canvas"
                    class="qr-image-on-canvas"
                  />
                  <div v-else class="qr-placeholder-on-canvas">
                    QR 코드
                  </div>
                </div>
              </DraggableResizableVue3>
            </div>
            </div>
          </DraggableResizableVue3>
        </div>
      </div>
      <div class="field-note">
        캔버스 박스의 크기를 자유롭게 조절할 수 있습니다. 상하좌우 선 가운데의 핸들을 드래그하여 각 방향별로 독립적으로 크기를 조절하세요.
        <br>• 좌측/우측 핸들: 너비 조절
        <br>배경 이미지 위치: X={{ mmBackgroundPositionX }}mm, Y={{ mmBackgroundPositionY }}mm (크기: {{ mmBackgroundWidth }}mm × {{ mmBackgroundHeight }}mm)
      </div>
    </div>
    <button type="button" @click="resetCanvasOptions" class="btn btn-secondary btn-sm reset-btn" :disabled="isEditMode">스타일 초기화</button>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import DraggableResizableVue3 from 'draggable-resizable-vue3';

// Props 정의
const props = defineProps({
  isEditMode: {
    type: Boolean,
    default: false
  },
  useA4Canvas: {
    type: Boolean,
    default: false
  },
  useBackgroundImage: {
    type: Boolean,
    default: false
  },
  a4CanvasX: {
    type: Number,
    default: 0
  },
  a4CanvasY: {
    type: Number,
    default: 0
  },
  a4CanvasWidth: {
    type: Number,
    default: 793
  },
  a4CanvasHeight: {
    type: Number,
    default: 1123
  },
  backgroundImagePreview: {
    type: String,
    default: ''
  },
  backgroundImageFile: {
    type: File,
    default: null
  },
  backgroundFitMode: {
    type: String,
    default: 'fit'
  },
  backgroundPositionX: {
    type: Number,
    default: 0
  },
  backgroundPositionY: {
    type: Number,
    default: 0
  },
  backgroundWidth: {
    type: Number,
    default: 200
  },
  backgroundHeight: {
    type: Number,
    default: 200
  },
  qrCodeData: {
    type: String,
    default: ''
  },
  qrPositionX: {
    type: Number,
    default: 50
  },
  qrPositionY: {
    type: Number,
    default: 50
  },
  qrWidth: {
    type: Number,
    default: 100
  },
  qrHeight: {
    type: Number,
    default: 100
  },
  imageLoadError: {
    type: Boolean,
    default: false
  }
});

// Emits 정의
const emit = defineEmits([
  'update:useA4Canvas',
  'update:useBackgroundImage',
  'update:a4CanvasX',
  'update:a4CanvasY',
  'update:a4CanvasWidth',
  'update:a4CanvasHeight',
  'update:backgroundImagePreview',
  'update:backgroundImageFile',
  'update:backgroundFitMode',
  'update:backgroundPositionX',
  'update:backgroundPositionY',
  'update:backgroundWidth',
  'update:backgroundHeight',
  'update:qrPositionX',
  'update:qrPositionY',
  'update:qrWidth',
  'update:qrHeight',
  'background-upload',
  'background-remove',
  'canvas-reset',
  'image-error'
]);

// 로컬 상태
const backgroundInput = ref(null);
const a4CanvasContainer = ref(null);
const isBackgroundImageActive = ref(false);
const isQrCodeActive = ref(false);

// 양방향 바인딩을 위한 로컬 상태
const useA4Canvas = ref(props.useA4Canvas);
const useBackgroundImage = ref(props.useBackgroundImage);
const a4CanvasX = ref(props.a4CanvasX);
const a4CanvasY = ref(props.a4CanvasY);
const a4CanvasWidth = ref(props.a4CanvasWidth);
const a4CanvasHeight = ref(props.a4CanvasHeight);
const backgroundImagePreview = ref(props.backgroundImagePreview);
const backgroundFitMode = ref(props.backgroundFitMode);
const backgroundPositionX = ref(props.backgroundPositionX);
const backgroundPositionY = ref(props.backgroundPositionY);
const backgroundWidth = ref(props.backgroundWidth);
const backgroundHeight = ref(props.backgroundHeight);
const qrPositionX = ref(props.qrPositionX);
const qrPositionY = ref(props.qrPositionY);
const qrWidth = ref(props.qrWidth);
const qrHeight = ref(props.qrHeight);

// px를 cm로 변환하기 위한 computed 속성
const PX_TO_CM_RATIO = 2.54 / 96; // 96DPI 기준
const cmA4CanvasWidth = computed(() => (a4CanvasWidth.value * PX_TO_CM_RATIO).toFixed(2));
const cmA4CanvasHeight = computed(() => (a4CanvasHeight.value * PX_TO_CM_RATIO).toFixed(2));

// mm 단위 변환
const mmBackgroundPositionX = computed(() => (backgroundPositionX.value * PX_TO_CM_RATIO * 10).toFixed(1));
const mmBackgroundPositionY = computed(() => (backgroundPositionY.value * PX_TO_CM_RATIO * 10).toFixed(1));
const mmBackgroundWidth = computed(() => (backgroundWidth.value * PX_TO_CM_RATIO * 10).toFixed(1));
const mmBackgroundHeight = computed(() => (backgroundHeight.value * PX_TO_CM_RATIO * 10).toFixed(1));

// 배경 이미지 스타일
const backgroundImageStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    objectFit: backgroundFitMode.value === 'fill' ? 'cover' : 
               backgroundFitMode.value === 'fit' ? 'contain' : 'none'
  };
});

// Props 변경 감지
watch(() => props.useA4Canvas, (newValue) => useA4Canvas.value = newValue);
watch(() => props.useBackgroundImage, (newValue) => useBackgroundImage.value = newValue);
watch(() => props.backgroundImagePreview, (newValue) => backgroundImagePreview.value = newValue);

// 로컬 상태 변경을 부모로 전달
watch(useA4Canvas, (newValue) => emit('update:useA4Canvas', newValue));
watch(useBackgroundImage, (newValue) => emit('update:useBackgroundImage', newValue));
watch(a4CanvasX, (newValue) => emit('update:a4CanvasX', newValue));
watch(a4CanvasY, (newValue) => emit('update:a4CanvasY', newValue));
watch(a4CanvasWidth, (newValue) => emit('update:a4CanvasWidth', newValue));
watch(a4CanvasHeight, (newValue) => emit('update:a4CanvasHeight', newValue));
watch(backgroundPositionX, (newValue) => emit('update:backgroundPositionX', newValue));
watch(backgroundPositionY, (newValue) => emit('update:backgroundPositionY', newValue));
watch(backgroundWidth, (newValue) => emit('update:backgroundWidth', newValue));
watch(backgroundHeight, (newValue) => emit('update:backgroundHeight', newValue));
watch(qrPositionX, (newValue) => emit('update:qrPositionX', newValue));
watch(qrPositionY, (newValue) => emit('update:qrPositionY', newValue));
watch(qrWidth, (newValue) => emit('update:qrWidth', newValue));
watch(qrHeight, (newValue) => emit('update:qrHeight', newValue));

// 배경 이미지 업로드 처리
const handleBackgroundUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 파일 크기 검사 (5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    alert('파일 크기가 5MB를 초과합니다. 더 작은 파일을 선택해주세요.');
    return;
  }

  // 이미지 파일 검사
  if (!file.type.startsWith('image/')) {
    alert('이미지 파일만 업로드할 수 있습니다.');
    return;
  }

  // 파일과 미리보기 URL 생성
  const reader = new FileReader();
  reader.onload = (e) => {
    backgroundImagePreview.value = e.target.result;
    emit('update:backgroundImageFile', file);
    emit('background-upload', { file, preview: e.target.result });
  };
  reader.readAsDataURL(file);
};

// 배경 이미지 제거
const removeBackgroundImage = () => {
  backgroundImagePreview.value = '';
  emit('update:backgroundImageFile', null);
  emit('background-remove');
  
  // 파일 입력 요소 리셋
  if (backgroundInput.value) {
    backgroundInput.value.value = '';
  }
};

// 배경 이미지 맞춤 모드 설정
const setBackgroundFitMode = (mode) => {
  backgroundFitMode.value = mode;
  emit('update:backgroundFitMode', mode);
};

// 캔버스 옵션 초기화
const resetCanvasOptions = () => {
  emit('canvas-reset');
};

// 이미지 에러 처리
const handleImageError = (event) => {
  // no-image 이미지로 대체
  event.target.src = '/src/assets/image/no-image.png';
  emit('image-error', event);
};
</script>

<style scoped>
.a4-canvas-section {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.design-group {
  margin-bottom: 15px;
}

.toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.toggle-container label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 25px;
}

.toggle-switch input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 25px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 19px;
  width: 19px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: #007bff;
}

.toggle-switch input:checked + label:before {
  transform: translateX(25px);
}

.field-note {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
}

.background-image-container {
  margin-top: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.bg-image-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.bg-image-name {
  font-size: 14px;
  color: #333;
}

.secondary-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.secondary-btn:hover {
  background-color: #5a6268;
}

.bg-upload-outside {
  margin-bottom: 15px;
}

.background-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background-color: #28a745;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.background-upload-btn:hover {
  background-color: #218838;
}

.upload-icon {
  font-size: 16px;
  font-weight: bold;
}

.background-file-input {
  display: none;
}

.canvas-toolbar {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.image-controls {
  display: flex;
  gap: 5px;
}

.control-btn {
  padding: 5px 10px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
}

.control-btn:hover {
  background-color: #f8f9fa;
}

.control-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.a4-canvas-section h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.canvas-size-display {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.a4-canvas {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
  min-height: 400px;
}

.a4-canvas-parent-container {
  position: relative;
  width: 100%;
  min-height: 600px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid #ccc;
  overflow: hidden;
}

.qr-image-on-canvas,
.background-image-on-canvas {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qr-placeholder-on-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  color: #666;
  font-size: 12px;
  border: 1px dashed #ccc;
}

.inactive-draggable {
  opacity: 0.7;
}

.reset-btn {
  margin-top: 15px;
  padding: 8px 16px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.reset-btn:hover {
  background-color: #5a6268;
}

.reset-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
