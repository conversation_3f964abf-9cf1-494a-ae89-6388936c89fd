[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:QrCodeForm.vue 모듈화 계획 수립 DESCRIPTION:QrCodeForm.vue 파일을 10단계로 나누어 모듈화하는 전체 계획을 수립하고 각 단계별 작업 내용을 정의합니다.
-[x] NAME:1단계: 기본 폼 필드 컴포넌트 분리 DESCRIPTION:QR 코드 이름, 타입, 상태, 설명 등 기본 폼 필드들을 별도 컴포넌트로 분리합니다. (BasicFormFields.vue)
-[x] NAME:2단계: 엑셀 업로드 컴포넌트 분리 DESCRIPTION:엑셀 파일 업로드 및 처리 관련 기능을 별도 컴포넌트로 분리합니다. (ExcelUploadSection.vue)
-[x] NAME:3단계: QR 타입별 입력 컴포넌트 분리 DESCRIPTION:랜딩페이지, 이벤트, 위치, Wi-Fi 등 QR 타입별 특화 입력 필드들을 별도 컴포넌트로 분리합니다. (QrTypeInputs.vue)
-[ ] NAME:4단계: 위치 정보 입력 컴포넌트 분리 DESCRIPTION:QR 코드 설치 위치 입력 및 카카오맵 연동 기능을 별도 컴포넌트로 분리합니다. (LocationInputSection.vue)
-[ ] NAME:5단계: 디자인 옵션 컴포넌트 분리 DESCRIPTION:QR 코드 디자인 옵션 (색상, 로고, 오류 복원 수준 등)을 별도 컴포넌트로 분리합니다. (DesignOptionsSection.vue)
-[ ] NAME:6단계: A4 캔버스 컴포넌트 분리 DESCRIPTION:A4 캔버스 및 배경 이미지 관련 기능을 별도 컴포넌트로 분리합니다. (A4CanvasSection.vue)
-[ ] NAME:7단계: QR 코드 미리보기 컴포넌트 분리 DESCRIPTION:QR 코드 미리보기 및 스캔율 표시 기능을 별도 컴포넌트로 분리합니다. (QrPreviewSection.vue)
-[ ] NAME:8단계: 배치 생성 결과 컴포넌트 분리 DESCRIPTION:QR 코드 연속 생성 결과 표시 기능을 별도 컴포넌트로 분리합니다. (BatchResultsSection.vue)
-[ ] NAME:9단계: 공통 유틸리티 함수 분리 DESCRIPTION:날짜 포맷팅, 유효성 검사, QR 코드 생성 등 공통 유틸리티 함수들을 별도 파일로 분리합니다. (qrFormUtils.js)
-[ ] NAME:10단계: 상태 관리 컴포저블 분리 DESCRIPTION:폼 데이터 관리, 디자인 옵션 관리 등 상태 관리 로직을 컴포저블로 분리합니다. (useQrForm.js, useQrDesign.js)