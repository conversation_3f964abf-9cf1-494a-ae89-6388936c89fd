<template>
  <div class="qr-preview-section">
    <!-- QR 코드 미리보기 섹션 -->
    <div class="qr-code-preview">
      <h3>QR 코드 미리보기</h3>
      <div v-if="isEditMode" class="edit-mode-notice">
        <strong>수정 모드에서는 QR 코드 미리보기만 가능합니다.</strong>
      </div>
      
      <!-- 클라이언트에서 생성한 QR 코드 미리보기 -->
      <div ref="qrCodePreviewContainer" class="qr-preview-container">
        <img
          v-if="qrImageUrl && !imageLoadError"
          :src="qrImageUrl"
          alt="QR Code Image"
          class="qr-image"
          @error="handleImageError"
        />
      </div>
      
      <div v-if="!hasTargetContent" class="image-placeholder">
        타겟 콘텐츠를 입력하면 QR 코드가 보입니다.<br>(샘플 이미지이며 생성 요청시 실제 QR코드가 생성됩니다.)
      </div>

      <!-- QR 코드 다운로드 버튼 -->
      <div v-if="hasTargetContent && !isEditMode" class="qr-download-section">
        <button 
          type="button" 
          @click="downloadQrCode" 
          class="download-btn"
          :disabled="!isQrCodeGenerated"
        >
          QR 코드 다운로드 (PNG)
        </button>
      </div>

      <!-- QR 코드 스캔율 표시 (수정 모드에서는 표시하지 않음) -->
      <div v-if="hasTargetContent && !isEditMode" class="scan-reliability-container">
        <div class="scan-reliability-label">
          <span>스캔율:</span>
          <span :class="scanReliabilityClass">{{ scanReliability }}%</span>
        </div>
        <div class="scan-reliability-progress-container">
          <div 
            class="scan-reliability-progress" 
            :style="{ width: `${scanReliability}%`, backgroundColor: scanReliabilityColor }"
          ></div>
        </div>
        <div class="scan-reliability-note">
          {{ scanReliabilityMessage }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import QRCodeStyling from 'qr-code-styling';

// Props 정의
const props = defineProps({
  isEditMode: {
    type: Boolean,
    default: false
  },
  qrImageUrl: {
    type: String,
    default: ''
  },
  hasTargetContent: {
    type: Boolean,
    default: false
  },
  targetContent: {
    type: String,
    default: ''
  },
  qrType: {
    type: String,
    default: ''
  },
  qrColor: {
    type: String,
    default: '#000000'
  },
  qrBgColor: {
    type: String,
    default: '#FFFFFF'
  },
  qrEyeColor: {
    type: String,
    default: '#000000'
  },
  qrEyeStyle: {
    type: Number,
    default: 0
  },
  qrDotsStyle: {
    type: Number,
    default: 0
  },
  qrErrorCorrectionLevel: {
    type: String,
    default: 'M'
  },
  logoPreview: {
    type: String,
    default: ''
  },
  logoSize: {
    type: Number,
    default: 40
  },
  useA4Canvas: {
    type: Boolean,
    default: false
  },
  isDesignUpdateOnly: {
    type: Boolean,
    default: false
  },
  excelSampleData: {
    type: Object,
    default: () => ({
      isActive: false,
      qrType: '',
      targetContent: ''
    })
  }
});

// Emits 정의
const emit = defineEmits([
  'qr-generated',
  'qr-download',
  'image-error',
  'scan-reliability-calculated'
]);

// 로컬 상태
const qrCodePreviewContainer = ref(null);
const qrCodeInstance = ref(null);
const imageLoadError = ref(false);
const isQrCodeGenerated = ref(false);
const scanReliability = ref(85);

// QR 코드 스캔율 관련 computed 속성
const scanReliabilityClass = computed(() => {
  if (scanReliability.value >= 80) return 'scan-reliability-high';
  if (scanReliability.value >= 60) return 'scan-reliability-medium';
  return 'scan-reliability-low';
});

const scanReliabilityColor = computed(() => {
  if (scanReliability.value >= 80) return '#4CAF50'; // 녹색
  if (scanReliability.value >= 60) return '#FF9800'; // 주황색
  return '#F44336'; // 빨간색
});

const scanReliabilityMessage = computed(() => {
  if (scanReliability.value >= 80) return '스캔이 매우 잘 됩니다.';
  if (scanReliability.value >= 60) return '스캔이 보통 수준입니다.';
  return '스캔이 어려울 수 있습니다.';
});

// QR 코드 미리보기 생성
const generateClientSideQrCode = async () => {
  if (!qrCodePreviewContainer.value) {
    return;
  }

  // 엑셀 샘플 데이터가 있는 경우 사용하거나, 기본 타겟 콘텐츠 사용
  let contentToUse = '';
  let qrTypeToUse = props.qrType;
  
  if (props.excelSampleData.isActive && props.excelSampleData.targetContent) {
    contentToUse = props.excelSampleData.targetContent;
    qrTypeToUse = props.excelSampleData.qrType;
  } else {
    contentToUse = props.targetContent;
  }

  if (!contentToUse) {
    return;
  }

  const qrSize = 150; // QR 코드 크기

  // QR 코드 스타일링 라이브러리용 옵션 구성
  const baseDesignOptions = {
    width: qrSize,
    height: qrSize,
    data: contentToUse,
    margin: 0,
    dotsOptions: {
      color: props.qrColor,
      type: props.qrDotsStyle === 0 ? 'square' : 'dots'
    },
    backgroundOptions: {
      color: props.qrBgColor,
    },
    cornersSquareOptions: {
      color: props.qrEyeColor,
      type: props.qrEyeStyle === 0 ? 'square' : 'dot',
    },
    cornersDotOptions: {
      color: props.qrEyeColor,
      type: props.qrEyeStyle === 0 ? undefined : 'dot'
    },
    qrOptions: {
      errorCorrectionLevel: props.qrErrorCorrectionLevel
    }
  };

  // 로고가 있는 경우 이미지 옵션 추가
  if (props.logoPreview) {
    baseDesignOptions.imageOptions = {
      hideBackgroundDots: true,
      imageSize: props.logoSize / 100, // 퍼센트를 0-1 사이 값으로 변환
      margin: 0
    };
    baseDesignOptions.image = props.logoPreview;
  }

  try {
    if (!qrCodeInstance.value) {
      // 컨테이너 비우기
      while (qrCodePreviewContainer.value.firstChild) {
        qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
      }
      qrCodeInstance.value = new QRCodeStyling(baseDesignOptions);
      await qrCodeInstance.value.append(qrCodePreviewContainer.value);
    } else {
      await qrCodeInstance.value.update(baseDesignOptions);
    }
    
    // QR 코드 생성/업데이트 후 스캔율 계산
    scanReliability.value = calculateScanReliability();
    isQrCodeGenerated.value = true;

    // 부모 컴포넌트에 QR 코드 생성 완료 알림
    emit('qr-generated', {
      qrCodeInstance: qrCodeInstance.value,
      scanReliability: scanReliability.value
    });

    // 스캔율 계산 결과 전달
    emit('scan-reliability-calculated', scanReliability.value);

  } catch (error) {
    console.error("QR 코드 생성/업데이트 실패:", error);
    qrCodePreviewContainer.value.innerHTML = '<p style="color: red; text-align: center; padding: 10px;">QR 코드 미리보기 생성 중 오류가 발생했습니다.</p>';
    qrCodeInstance.value = null;
  }
};

// 디바운스된 QR 코드 생성 함수
const generateQrCodeDebounced = (() => {
  let timeoutId;
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      generateClientSideQrCode();
    }, 300);
  };
})();

// QR 코드 스캔율 계산
const calculateScanReliability = () => {
  let reliability = 100;

  // 타겟 콘텐츠 길이에 따른 감점
  const contentLength = props.targetContent?.length || 0;
  if (contentLength > 100) reliability -= 15;
  else if (contentLength > 50) reliability -= 10;
  else if (contentLength > 25) reliability -= 5;

  // 오류 복원 수준에 따른 보정
  switch (props.qrErrorCorrectionLevel) {
    case 'L': reliability -= 10; break;
    case 'M': reliability -= 5; break;
    case 'Q': reliability += 0; break;
    case 'H': reliability += 5; break;
  }

  // 로고 크기에 따른 감점
  if (props.logoPreview) {
    if (props.logoSize > 30) reliability -= 15;
    else if (props.logoSize > 20) reliability -= 10;
    else if (props.logoSize > 10) reliability -= 5;
  }

  // 색상 대비에 따른 감점 (간단한 계산)
  if (props.qrColor === props.qrBgColor) reliability -= 50;
  
  return Math.max(30, Math.min(100, reliability));
};

// QR 코드 다운로드
const downloadQrCode = async () => {
  if (!qrCodeInstance.value) {
    alert('QR 코드가 생성되지 않았습니다.');
    return;
  }

  try {
    // PNG 형식으로 다운로드
    await qrCodeInstance.value.download({
      name: 'qr-code',
      extension: 'png'
    });

    emit('qr-download', 'png');
  } catch (error) {
    console.error('QR 코드 다운로드 실패:', error);
    alert('QR 코드 다운로드 중 오류가 발생했습니다.');
  }
};

// 이미지 에러 처리
const handleImageError = (event) => {
  imageLoadError.value = true;
  // no-image 이미지로 대체
  event.target.src = '/src/assets/image/no-image.png';
  emit('image-error', event);
};

// Props 변경 감지하여 QR 코드 재생성
watch([
  () => props.targetContent,
  () => props.qrColor,
  () => props.qrBgColor,
  () => props.qrEyeColor,
  () => props.qrEyeStyle,
  () => props.qrDotsStyle,
  () => props.qrErrorCorrectionLevel,
  () => props.logoPreview,
  () => props.logoSize,
  () => props.excelSampleData
], () => {
  if (props.hasTargetContent) {
    generateQrCodeDebounced();
  }
}, { deep: true });

// 컴포넌트 마운트 시 초기 QR 코드 생성
onMounted(() => {
  if (props.hasTargetContent) {
    nextTick(() => {
      generateClientSideQrCode();
    });
  }
});
</script>

<style scoped>
.qr-preview-section {
  margin-bottom: 20px;
}

.qr-code-preview {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background-color: #f9f9f9;
}

.qr-code-preview h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #333;
}

.edit-mode-notice {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  color: #856404;
}

.qr-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
  margin-bottom: 15px;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 4px;
}

.qr-image {
  max-width: 150px;
  max-height: 150px;
  border-radius: 4px;
}

.image-placeholder {
  text-align: center;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.qr-download-section {
  text-align: center;
  margin-bottom: 15px;
}

.download-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.download-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.download-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.scan-reliability-container {
  margin-top: 15px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #eee;
}

.scan-reliability-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.scan-reliability-high {
  color: #4CAF50;
  font-weight: bold;
}

.scan-reliability-medium {
  color: #FF9800;
  font-weight: bold;
}

.scan-reliability-low {
  color: #F44336;
  font-weight: bold;
}

.scan-reliability-progress-container {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.scan-reliability-progress {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.scan-reliability-note {
  font-size: 12px;
  color: #666;
  text-align: center;
}
</style>
